package com.phodal.legacy;

import picocli.CommandLine;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.Parameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.phodal.legacy.services.AnalysisService;
import com.phodal.legacy.generator.application.SpringBootCodeGenerator;
import com.phodal.legacy.generator.application.GenerationConfig;
import com.phodal.legacy.generator.ConversionResult;

import java.io.File;
import java.util.concurrent.Callable;

/**
 * Main CLI application for converting legacy JSP projects to Spring Boot applications.
 * 
 * This tool provides automated migration capabilities for legacy JSP-based web applications,
 * transforming them into modern Spring Boot applications with proper structure and dependencies.
 */
@SpringBootApplication
@Command(
    name = "jsp2springboot",
    mixinStandardHelpOptions = true,
    version = "jsp2springboot 1.0.0",
    description = "Convert legacy JSP projects to Spring Boot applications",
    subcommands = {
        CliApp.AnalyzeCommand.class,
        CliApp.ConvertCommand.class,
        CliApp.ValidateCommand.class
    }
)
public class CliApp implements Callable<Integer> {
    
    private static final Logger logger = LoggerFactory.getLogger(CliApp.class);
    
    @Option(names = {"-v", "--verbose"}, description = "Enable verbose logging")
    private boolean verbose = false;
    
    @Option(names = {"-q", "--quiet"}, description = "Enable quiet mode (minimal output)")
    private boolean quiet = false;
    
    public static void main(String[] args) {
        // Configure logging before starting
        configureLogging();
        
        logger.info("Starting JSP to Spring Boot migration tool...");
        
        int exitCode = new CommandLine(new CliApp()).execute(args);
        
        logger.info("Migration tool finished with exit code: {}", exitCode);
        System.exit(exitCode);
    }
    
    @Override
    public Integer call() throws Exception {
        logger.info("JSP to Spring Boot Migration Tool v1.0.0");
        logger.info("Use --help to see available commands");
        logger.info("Available commands: analyze, convert, validate");
        return 0;
    }
    
    /**
     * Configure logging based on command line options
     */
    private static void configureLogging() {
        // Set default logging level
        System.setProperty("logging.level.com.phodal.legacy", "INFO");
        System.setProperty("logging.pattern.console", 
            "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n");
    }
    
    /**
     * Analyze command to examine legacy JSP project structure
     */
    @Command(name = "analyze", description = "Analyze legacy JSP project structure and dependencies")
    static class AnalyzeCommand implements Callable<Integer> {
        
        private static final Logger logger = LoggerFactory.getLogger(AnalyzeCommand.class);
        
        @Parameters(index = "0", description = "Path to the legacy JSP project directory")
        private File projectPath;
        
        @Option(names = {"-o", "--output"}, description = "Output file for analysis report")
        private File outputFile;
        
        @Option(names = {"--include-jsp"}, description = "Include JSP file analysis", defaultValue = "true")
        private boolean includeJsp;
        
        @Option(names = {"--include-java"}, description = "Include Java source analysis", defaultValue = "true")
        private boolean includeJava;
        
        @Option(names = {"--include-web-xml"}, description = "Include web.xml analysis", defaultValue = "true")
        private boolean includeWebXml;
        
        @Override
        public Integer call() throws Exception {
            logger.info("Starting analysis of legacy JSP project: {}", projectPath.getAbsolutePath());
            
            if (!projectPath.exists() || !projectPath.isDirectory()) {
                logger.error("Project path does not exist or is not a directory: {}", projectPath);
                return 1;
            }
            
            logger.info("Analysis configuration:");
            logger.info("  - Include JSP files: {}", includeJsp);
            logger.info("  - Include Java sources: {}", includeJava);
            logger.info("  - Include web.xml: {}", includeWebXml);

            if (outputFile != null) {
                logger.info("  - Output report to: {}", outputFile.getAbsolutePath());
            }

            try {
                // Create analysis service and options
                AnalysisService analysisService = new AnalysisService();
                AnalysisService.AnalysisOptions options = new AnalysisService.AnalysisOptions();
                options.setIncludeJsp(includeJsp);
                options.setIncludeJava(includeJava);
                options.setIncludeWebXml(includeWebXml);

                // Perform analysis
                AnalysisService.AnalysisResult result = analysisService.analyzeProject(
                    projectPath.toPath(), options);

                if (result.isSuccess()) {
                    logger.info("Analysis completed successfully in {} ms", result.getDuration());

                    // Log summary
                    AnalysisService.AnalysisSummary summary = result.getSummary();
                    if (summary != null) {
                        logger.info("Project analysis summary:");
                        logger.info("  - Total components: {}",
                            summary.getComponentCounts().values().stream().mapToInt(Integer::intValue).sum());
                        logger.info("  - Migration complexity: {}", summary.getMigrationComplexity());

                        if (summary.getGraphStatistics() != null) {
                            logger.info("  - Dependency graph: {}", summary.getGraphStatistics());
                        }
                    }

                    logger.info("Project is ready for conversion");
                    return 0;
                } else {
                    logger.error("Analysis failed: {}", result.getErrorMessage());
                    return 1;
                }

            } catch (Exception e) {
                logger.error("Analysis failed with exception: {}", e.getMessage(), e);
                return 1;
            }
        }
    }
    
    /**
     * Convert command to perform the actual migration
     */
    @Command(name = "convert", description = "Convert legacy JSP project to Spring Boot application")
    static class ConvertCommand implements Callable<Integer> {
        
        private static final Logger logger = LoggerFactory.getLogger(ConvertCommand.class);
        
        @Parameters(index = "0", description = "Path to the legacy JSP project directory")
        private File sourcePath;
        
        @Parameters(index = "1", description = "Path to the output Spring Boot project directory")
        private File targetPath;
        
        @Option(names = {"--spring-boot-version"}, description = "Target Spring Boot version", defaultValue = "3.2.1")
        private String springBootVersion;
        
        @Option(names = {"--java-version"}, description = "Target Java version", defaultValue = "17")
        private String javaVersion;
        
        @Option(names = {"--package-name"}, description = "Base package name for generated code", defaultValue = "com.example.migrated")
        private String packageName;
        
        @Override
        public Integer call() throws Exception {
            logger.info("Starting conversion from JSP to Spring Boot");
            logger.info("Source project: {}", sourcePath.getAbsolutePath());
            logger.info("Target project: {}", targetPath.getAbsolutePath());
            logger.info("Spring Boot version: {}", springBootVersion);
            logger.info("Java version: {}", javaVersion);
            logger.info("Base package: {}", packageName);
            
            if (!sourcePath.exists() || !sourcePath.isDirectory()) {
                logger.error("Source path does not exist or is not a directory: {}", sourcePath);
                return 1;
            }
            
            if (targetPath.exists()) {
                logger.warn("Target directory already exists: {}", targetPath.getAbsolutePath());
                logger.warn("Existing files may be overwritten");
            }

            try {
                // Step 1: Analyze the source project
                logger.info("Analyzing source project...");
                AnalysisService analysisService = new AnalysisService();
                AnalysisService.AnalysisOptions options = new AnalysisService.AnalysisOptions();
                options.setIncludeJsp(true);
                options.setIncludeJava(true);
                options.setIncludeWebXml(true);

                AnalysisService.AnalysisResult analysisResult = analysisService.analyzeProject(
                    sourcePath.toPath(), options);

                if (!analysisResult.isSuccess()) {
                    logger.error("Failed to analyze source project: {}", analysisResult.getErrorMessage());
                    return 1;
                }

                logger.info("Analysis completed. Found {} JSP, {} Java, {} web.xml components",
                    analysisResult.getJspComponents().size(),
                    analysisResult.getJavaComponents().size(),
                    analysisResult.getWebXmlComponents().size());

                // Step 2: Configure code generation
                GenerationConfig config = new GenerationConfig(springBootVersion, javaVersion, packageName);

                // Step 3: Generate Spring Boot application
                logger.info("Starting code generation...");
                SpringBootCodeGenerator generator = new SpringBootCodeGenerator(config);
                ConversionResult result = generator.convertProject(
                    sourcePath.toPath(), targetPath.toPath(), analysisResult);

                if (!result.isSuccess()) {
                    logger.error("Conversion failed: {}", result.getErrorMessage());
                    return 1;
                }

                // Step 4: Report results
                logger.info("Conversion completed successfully!");
                logger.info("Generated Spring Boot project at: {}", targetPath.getAbsolutePath());
                logger.info("Generated {} classes:", result.getGeneratedClassCount());
                logger.info("  - Controllers: {}", result.getControllerCount());
                logger.info("  - Services: {}", result.getServiceCount());
                logger.info("  - Entities: {}", result.getEntityCount());
                logger.info("  - Repositories: {}", result.getRepositoryCount());

                if (!result.getWarnings().isEmpty()) {
                    logger.warn("Conversion completed with {} warnings:", result.getWarnings().size());
                    result.getWarnings().forEach(warning -> logger.warn("  - {}", warning));
                }

                return 0;

            } catch (Exception e) {
                logger.error("Conversion failed with exception: {}", e.getMessage(), e);
                return 1;
            }
        }
    }
    
    /**
     * Validate command to check the converted project
     */
    @Command(name = "validate", description = "Validate converted Spring Boot project")
    static class ValidateCommand implements Callable<Integer> {
        
        private static final Logger logger = LoggerFactory.getLogger(ValidateCommand.class);
        
        @Parameters(index = "0", description = "Path to the Spring Boot project directory")
        private File projectPath;
        
        @Option(names = {"--build-test"}, description = "Run build test", defaultValue = "true")
        private boolean buildTest;
        
        @Option(names = {"--unit-test"}, description = "Run unit tests", defaultValue = "true")
        private boolean unitTest;
        
        @Override
        public Integer call() throws Exception {
            logger.info("Starting validation of Spring Boot project: {}", projectPath.getAbsolutePath());
            
            if (!projectPath.exists() || !projectPath.isDirectory()) {
                logger.error("Project path does not exist or is not a directory: {}", projectPath);
                return 1;
            }
            
            logger.info("Validation configuration:");
            logger.info("  - Build test: {}", buildTest);
            logger.info("  - Unit test: {}", unitTest);
            
            // TODO: Implement actual validation logic
            logger.info("Validation completed successfully");
            logger.info("Project is ready for deployment");
            
            return 0;
        }
    }
}
